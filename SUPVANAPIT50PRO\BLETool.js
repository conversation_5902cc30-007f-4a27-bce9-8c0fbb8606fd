import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import supPrintUtilsMP50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import supPrintUtilsG15 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import supPrintUtilsG21 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import lpapi from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004C\u0050\u0041\u0050\u0049\u002F\u004C\u0050\u0041\u0050\u0049\u002E\u006A\u0073";export default{'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,"CMD_READ_DPI":0x22,'\u0053\u0055\u0050\u0050\u004F\u0052\u0054\u005F\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044':["\u0046\u0045\u0045\u0037","FF0E".split("").reverse().join("")],"SERVICE_UUID":"\u0045\u0030\u0046\u0046","NOTIFY_UUID":"\u0046\u0046\u0045\u0031",'\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044':"FFE9","deviceId":'','\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':'','\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064':'','\u0077\u0072\u0069\u0074\u0065\u0049\u0064':'',"searchBlueList":[],'\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065':false,"deviceSn":'','\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,"isCustomType":!![],wxPromise(api,options={}){return new Promise((resolve,reject)=>{api({...options,"success":res=>resolve(res),"fail":e=>reject(e)});});},async scanBleDeviceList(callBack){var _0x2e3da;const that=this;_0x2e3da=651975^651968;that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']=[];var _0x26agg=(749517^749517)+(421499^421497);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0031'];_0x26agg=(246501^246499)+(644949^644946);try{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006F\u0070\u0065\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072']);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0032'];const stateRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072\u0053\u0074\u0061\u0074\u0065']);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0033'];if(!stateRes&&!stateRes['\u0061\u0076\u0061\u0069\u006C\u0061\u0062\u006C\u0065']){resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0035'];return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u7528\u53EF\u4E0D\u5668\u914D\u9002\u7259\u84DD".split("").reverse().join(""));}await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u0061\u0072\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079'],{'\u0061\u006C\u006C\u006F\u0077\u0044\u0075\u0070\u006C\u0069\u0063\u0061\u0074\u0065\u0073\u004B\u0065\u0079':!![]});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0034'];wx['\u006F\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0046\u006F\u0075\u006E\u0064'](res=>{if(res&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][809663^809663]&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][781670^781670]['\u006E\u0061\u006D\u0065']&&that['\u0061\u006C\u006C\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][953650^953650]['\u006E\u0061\u006D\u0065'])){if(!that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0069\u006E\u0063\u006C\u0075\u0064\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][937871^937871]['\u006E\u0061\u006D\u0065'])){that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][109993^109993]['\u006E\u0061\u006D\u0065']);callBack(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](res));}}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async stopScanBleDevices(){try{await this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u006F\u0070\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'停止搜索蓝牙设备成功'});}catch(error){let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0037'];throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async disconnectBleDevice(){const that=this;try{if(that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']){if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await lpapi['\u0063\u006C\u006F\u0073\u0065\u0050\u0072\u0069\u006E\u0074\u0065\u0072']();return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u65AD\u5F00\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}else{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u006C\u006F\u0073\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"msg":'断开蓝牙设备成功'});}}else{var _0x337d8e=(541128^541129)+(487927^487924);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0037'];_0x337d8e=(577582^577581)+(716626^716634);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u7A7A\u4E3A\u53F7\u5907\u8BBE\u7259\u84DD".split("").reverse().join(""));}}catch(error){console['\u006C\u006F\u0067'](error);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0036'];throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async connectBleDevice(nBleDeviceInfo){try{const that=this;var _0x5b_0x578=(708952^708954)+(648411^648415);let bleDeviceInfo=nBleDeviceInfo;_0x5b_0x578=506753^506758;if(bleDeviceInfo){that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=bleDeviceInfo['\u006E\u0061\u006D\u0065'];if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){if(bleDeviceInfo['\u006E\u0061\u006D\u0065']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0044\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u006E\u0061\u006D\u0065']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'连接德佟蓝牙设备成功'});}}else{if(bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0053\u0075\u0070\u0076\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'连接硕方蓝牙设备成功','\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0055\u0075\u0069\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});}}}}catch(error){throw error;}},async connectDtBleDevice(nBluetoothName){let bluetoothName=nBluetoothName;return new Promise((resolve,reject)=>{lpapi['\u006F\u0070\u0065\u006E\u0050\u0072\u0069\u006E\u0074\u0065\u0072'](bluetoothName,res=>{resolve(res);},error=>{reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0039'],error));});});},async connectSupvanBleDevice(nDeviceId){var _0xagf3f;let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0030'];_0xagf3f=671880^671880;try{const that=this;var _0x67eb1f=(867062^867056)+(290502^290497);let deviceId=nDeviceId;_0x67eb1f=(357427^357434)+(181555^181559);that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']=deviceId;that['\u0073\u0065\u0074\u0050\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C'](wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u006D\u006F\u0064\u0065\u006C']);await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0072\u0065\u0061\u0074\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0031'];that['\u0062\u006C\u0065\u004D\u0074\u0075']();await that['\u0073\u0074\u006F\u0070\u0053\u0063\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073']();resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0032'];const bleDeviceServicesRes=await that['\u0067\u0065\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073']();resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0033'];bleDeviceServicesRes['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0073']['\u006D\u0061\u0070'](i=>{for(const s of this['\u0053\u0055\u0050\u0050\u004F\u0052\u0054\u005F\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']){if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(141994^141986)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](947916^947912,484927^484919)==s){this['\u0075\u0070\u0064\u0061\u0074\u0061\u0042\u006C\u0065\u0055\u0075\u0069\u0064'](s);this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}}});var _0x128de=(633900^633900)+(455206^455206);const bleDeviceCharacteristicsRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073'],{"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"serviceId":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});_0x128de=528958^528954;resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0034'];bleDeviceCharacteristicsRes['\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073']['\u006D\u0061\u0070'](i=>{if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(201260^201252)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](388164^388160,168966^168974)==this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']){this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}else if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](146796^146792,528488^528480)==this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']){this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}});await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006E\u006F\u0074\u0069\u0066\u0079\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'],{'\u0073\u0074\u0061\u0074\u0065':!![],'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"serviceId":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0049\u0064':this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0035'];wx['\u006F\u006E\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'](res=>{if(that['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtils['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsMP50['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG15['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG21['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},updataBleUuid(currentServiceUuid){switch(currentServiceUuid){case"7EEF".split("").reverse().join(""):this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="7EEF".split("").reverse().join("");this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0043\u0031";this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="1CEF".split("").reverse().join("");break;case"\u0045\u0030\u0046\u0046":this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="FF0E".split("").reverse().join("");this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="\u0046\u0046\u0045\u0031";this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0046\u0045\u0039";break;default:break;}},async stopPrint(callback){try{const that=this;if(this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](!![]);}else{canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);}}catch(error){throw error;}},async bleMtu(){if(wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u0070\u006C\u0061\u0074\u0066\u006F\u0072\u006D']=="\u0061\u006E\u0064\u0072\u006F\u0069\u0064"){await new Promise(resolve=>setTimeout(resolve,667164^667124));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0065\u0074\u0042\u004C\u0045\u004D\u0054\u0055'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u006D\u0074\u0075':182});}return Promise['\u0072\u0065\u0073\u006F\u006C\u0076\u0065'];},async getBleDeviceServices(){await new Promise(resolve=>setTimeout(resolve,944196^945560));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});},onWriteBLECharacteristicValue(nbyteData){let byteData=nbyteData;console['\u006C\u006F\u0067']("\u53D1\u9001\u6570\u636E\u003A",byteData);if(byteData){const buffer=new ArrayBuffer(byteData['\u006C\u0065\u006E\u0067\u0074\u0068']);var _0x2f5b;const dataView=new DataView(buffer);_0x2f5b=(411930^411922)+(980682^980686);for(var i=172041^172041;i<byteData['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){dataView['\u0073\u0065\u0074\u0055\u0069\u006E\u0074\u0038'](i,byteData[i]);}wx['\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065']({'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"serviceId":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0049\u0064':this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064'],'\u0076\u0061\u006C\u0075\u0065':dataView['\u0062\u0075\u0066\u0066\u0065\u0072'],'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{},'\u0066\u0061\u0069\u006C':res=>{console['\u006C\u006F\u0067']("\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0020\u0066\u0061\u0069\u006C",res);}});}},t50ProBleDevices(nBluetoothName){var _0x188d1b;const bluetoothName=nBluetoothName;_0x188d1b=295990^295986;var _0x6327ef;const validPrefixes=["A0010T".split("").reverse().join(""),"\u0054\u0030\u0030\u0039\u0039\u0041","\u0054\u0030\u0030\u0034\u0036\u0041","\u0054\u0030\u0030\u0031\u0033\u0042","\u0054\u0030\u0030\u0032\u0030\u0042","\u0054\u0030\u0030\u0032\u0034\u0042","\u0054\u0030\u0030\u0039\u0037\u0041","\u0054\u0030\u0031\u0030\u0031\u0041","\u0054\u0030\u0031\u0031\u0034\u0041","\u0054\u0030\u0031\u0031\u0035\u0041","\u0054\u0030\u0031\u0031\u0032\u0041","\u0054\u0030\u0031\u0031\u0033\u0041","\u0054\u0030\u0031\u0034\u0035\u0042","\u0054\u0030\u0031\u0034\u0036\u0042","B9410T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0030\u0042","B1510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0032\u0042","\u0054\u0030\u0031\u0035\u0033\u0042","\u0054\u0030\u0031\u0035\u0034\u0042","\u0054\u0030\u0031\u0035\u0035\u0042","\u0054\u0030\u0031\u0035\u0036\u0042","B3010T".split("").reverse().join(""),"\u0054\u0030\u0030\u0032\u0031\u0042","B9510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0030\u0042","\u0054\u0030\u0031\u0036\u0031\u0042","B7610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0038\u0042","B9610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0032\u0042","\u0054\u0030\u0031\u0036\u0033\u0042","B5810T".split("").reverse().join("")];_0x6327ef=(660852^660853)+(259278^259277);if(bluetoothName){return validPrefixes['\u0073\u006F\u006D\u0065'](prefix=>bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068'](prefix));}return false;},t80BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0030\u0033\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0031\u0031\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0030\u0039\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0037\u0035\u0041")){return!![];}else{return false;}}},t50ProAndT80BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},mp50BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){return!![];}else{return false;}}},mp50OnlyBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3000PM".split("").reverse().join(""))){return!![];}else{return false;}}},dtBleDevices(nBluetoothName){var _0xe24fc;let bluetoothName=nBluetoothName;_0xe24fc=212158^212151;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0035\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0038\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("05PM".split("").reverse().join(""))){return!![];}else{return false;}}},g15BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4100G".split("").reverse().join(""))){return!![];}else{return false;}},e11BleDevices(nBluetoothName){var _0xe469c=(611189^611196)+(448467^448464);let bluetoothName=nBluetoothName;_0xe469c=(240419^240426)+(852178^852182);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0033\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A9310T".split("").reverse().join(""))){return!![];}else{return false;}},g11BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A1400G".split("").reverse().join(""))){return!![];}else{return false;}},g11ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B8200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5400G".split("").reverse().join(""))){return!![];}else{return false;}},g11MBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0033\u0041")){return!![];}else{return false;}},g11MPlusBleDevices(nBluetoothName){var _0xd252f=(822465^822472)+(907516^907512);let bluetoothName=nBluetoothName;_0xd252f="mmmjna".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0035\u0041")){return!![];}else{return false;}},g15MBleDevices(nBluetoothName){var _0xfebddd;let bluetoothName=nBluetoothName;_0xfebddd=(821324^821323)+(235578^235581);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0030\u0034")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A7100G".split("").reverse().join(""))){return!![];}else{return false;}},g15MProBleDevices(nBluetoothName){var _0xec9ccd;let bluetoothName=nBluetoothName;_0xec9ccd=113405^113402;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A8100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0038\u0042")){return!![];}else{return false;}},g15MiniBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3000G".split("").reverse().join(""))){return!![];}else{return false;}},g15ProBleDevices(nBluetoothName){var _0xg997bc=(819988^819985)+(640942^640940);let bluetoothName=nBluetoothName;_0xg997bc=539245^539244;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0036")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0035\u0042")){return!![];}else{return false;}},g18ProBleDevices(nBluetoothName){var _0x5bce;let bluetoothName=nBluetoothName;_0x5bce="clcaoq".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2400G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4400G".split("").reverse().join(""))){return!![];}else{return false;}},g19MPlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0037\u0041")){return!![];}else{return false;}},g19PlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0036\u0041")){return!![];}else{return false;}},g28BleDevices(nBluetoothName){var _0x71cfgg;let bluetoothName=nBluetoothName;_0x71cfgg=(797199^797192)+(378562^378571);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3400G".split("").reverse().join(""))){return!![];}else{return false;}},q11PlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0031\u0041")){return!![];}else{return false;}},q11ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3100D".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0033\u0042")){return!![];}else{return false;}},q15BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0038")){return!![];}else{return false;}},q15MiniBleDevices(nBluetoothName){var _0x2ebcb=(242212^242213)+(951534^951534);let bluetoothName=nBluetoothName;_0x2ebcb=(911769^911760)+(731454^731446);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0034\u0041")){return!![];}else{return false;}},q15ProBleDevices(nBluetoothName){var _0x544b;let bluetoothName=nBluetoothName;_0x544b=757977^757969;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0039\u0041")){return!![];}else{return false;}},q18BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0030\u0041")){return!![];}else{return false;}},q19PlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2100D".split("").reverse().join(""))){return!![];}else{return false;}},g21BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0039")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4100D".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("0400G".split("").reverse().join(""))){return!![];}else{return false;}},gSeriesBleDevices(nBluetoothName){var _0xe88d=(895633^895632)+(748748^748749);let bluetoothName=nBluetoothName;_0xe88d=(703316^703324)+(466330^466329);if(this['\u0067\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0038\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},allBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(this['\u0069\u0073\u0043\u0075\u0073\u0074\u006F\u006D\u0054\u0079\u0070\u0065']){if(this['\u0054\u0035\u0030\u0050\u0072\u006F\u0043\u0075\u0073\u0074\u006F\u006D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}else{if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}}},g21OrG28BleDevices(nBluetoothName){var _0x9c41f;let bluetoothName=nBluetoothName;_0x9c41f="gdeklf".split("").reverse().join("");if(bluetoothName){if(this['\u0067\u0032\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}},T50ProCustomBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){const len=bluetoothName['\u006C\u0065\u006E\u0067\u0074\u0068'];if(len['\u006C\u0065\u006E\u0067\u0074\u0068']<(519035^519028)){return false;}if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B5410T".split("").reverse().join(""))){var _0x2d1g=(806964^806962)+(909878^909873);const len=bluetoothName['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x2d1g=(238723^238727)+(246207^246202);if(bluetoothName[len-(552329^552333)]==="\u0044"){return!![];}return false;}else{return false;}}},setPhoneModel(nPhoneModel){var _0xfd7a5c;let phoneModel=nPhoneModel;_0xfd7a5c=(217404^217403)+(444404^444407);if(phoneModel){if(phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("e9 CC IM".split("").reverse().join(""))||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0050\u0044\u0056\u004D\u0030\u0030")||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("00MABP".split("").reverse().join(""))){this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=!![];}else{this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=false;}}},getPhoneModel(){return this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065'];},getFDpiValue(){return this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},setFDpiValue(nValue){var _0x37bcb;const that=this;_0x37bcb=635400^635403;let value=nValue;let bluetoothName=that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];if(bluetoothName){if(this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=value;}else if(this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=value;}else{this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=295249^295257;}}},getMaxDotValueType(nBluetoothName){var _0x4g_0xb3e;let bluetoothName=nBluetoothName;_0x4g_0xb3e='\u006B\u006C\u0069\u0065\u0070\u0064';if(this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){if(this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']==(519017^519013)){return!![];}return false;}else{return false;}},setCustomType(custom){this['\u0069\u0073\u0043\u0075\u0073\u0074\u006F\u006D\u0054\u0079\u0070\u0065']=custom;}};